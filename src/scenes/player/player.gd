class_name Player
extends Area2D

@export var _initial_paint_fuel: int
@export var _max_paint_fuel: int
@export var _tile_size: int
@export var _wallChecker: RayCast2D
@export var _cellChecker: RayCast2D
@export var _animation_player: AnimationPlayer

const ANIMATION_DURATION: float = 0.1

## Константы для физических слоев игрока
const PLAYER_LAYER_DEFAULT = 1 ## Слой игрока по умолчанию (на суше)
const PLAYER_LAYER_SUBMERGED = 6 ## Слой игрока в воде

var _paint_fuel: int = 0:
	get:
		return _paint_fuel
	set(value):
		_paint_fuel = min(max(value, 0), _max_paint_fuel)
		Pigeon.send(PlayerPaintFuelChangeMessage.new(_paint_fuel))

var _moving: bool = false:
	get:
		return _moving
	set(value):
		_moving = value
		if not value: # Если движение закончилось
			var underneath_cell: BaseCell = _get_cell_underneath()
			Pigeon.send(PlayerStopMessage.new(underneath_cell))

var _move_tween: Tween
var _dead: bool

func construct() -> void:
	_paint_fuel = _initial_paint_fuel

func _ready() -> void:
	Pigeon.subscribe(InputMessage.ID, self, _on_input)
	Pigeon.subscribe(DamageMessage.ID, self, _on_damage)
	Pigeon.subscribe(PaintFuelChangeMessage.ID, self, _on_paint_fuel_change)

func _exit_tree() -> void:
	Pigeon.unsubscribe(InputMessage.ID, self)
	Pigeon.unsubscribe(DamageMessage.ID, self)
	Pigeon.unsubscribe(PaintFuelChangeMessage.ID, self)

func _on_input(msg: InputMessage) -> void:
	if _moving or _dead:
		return
	match msg.input:
		InputMessage.InputType.MOVE_UP:
			_move_async(Vector2.UP)
		InputMessage.InputType.MOVE_DOWN:
			_move_async(Vector2.DOWN)
		InputMessage.InputType.MOVE_LEFT:
			_move_async(Vector2.LEFT)
		InputMessage.InputType.MOVE_RIGHT:
			_move_async(Vector2.RIGHT)

func _on_damage(msg: DamageMessage) -> void:
	# Сначала предлагаем обработать урон дочерним способностям
	for handler in get_tree().get_nodes_in_group("damage_handlers"):
		if handler.has_method("handle_damage"):
			var handled: bool = handler.call("handle_damage", msg)
			if handled:
				# Способность обработала урон. Выходим.
				return

	# Если ни одна способность не обработала урон, тогда умираем.
	_die()

func _on_paint_fuel_change(msg: PaintFuelChangeMessage) -> void:
	match msg.change:
		PaintFuelChangeMessage.Type.ADD:
			_paint_fuel += msg.amount
		PaintFuelChangeMessage.Type.REMOVE:
			_paint_fuel -= msg.amount

	Pigeon.send(PlayerPaintFuelChangeMessage.new(_paint_fuel))

func _move_async(direction: Vector2) -> void:
	_wallChecker.target_position = direction * _tile_size
	_wallChecker.force_raycast_update()

	if _wallChecker.is_colliding():
		return

	_cellChecker.target_position = direction * _tile_size
	_cellChecker.force_raycast_update()

	var collider: Object = _cellChecker.get_collider()

	if not (collider is BaseCell):
		return

	var destination_cell: BaseCell = collider as BaseCell
	var start_cell: BaseCell = _get_cell_underneath()

	Pigeon.send(PlayerMoveMessage.new(start_cell, destination_cell))

	_move_tween = create_tween()
	_move_tween.tween_property(
		self,
		"position",
		position + direction * _tile_size,
		ANIMATION_DURATION
	).set_trans(Tween.TRANS_SINE)
	_moving = true
	await _move_tween.finished
	_moving = false

	# Уведомляем клетку о том, что игрок на неё приземлился
	var cell: BaseCell = _get_cell_underneath()
	if cell:
		cell.on_player_landed(self)

func decrease_paint_fuel(amount: int) -> void:
	"""Публичный метод для уменьшения краски. Используется клетками."""
	_paint_fuel -= amount

func get_paint_fuel() -> int:
	"""Публичный метод для получения количества краски. Используется клетками."""
	return _paint_fuel

func _die() -> void:
	_dead = true
	_move_tween.stop()
	_moving = false
	_animation_player.play("die")
	Pigeon.send(PlayerDieMessage.new())

func _get_cell_underneath() -> BaseCell:
	var cells := get_overlapping_areas()
	if cells.size() == 0:
		return
	if cells.size() > 1:
		push_warning("There are more then 1 cell underneath player.")
		return

	var cell := cells[0] as BaseCell
	return cell

## Получает клетку на указанной позиции, используя детекцию пересечений областей
func _get_cell_at_position(target_position: Vector2) -> BaseCell:
	# Используем запрос к физическому миру для поиска областей на определенной позиции
	var space_state := get_world_2d().direct_space_state
	var query := PhysicsPointQueryParameters2D.new()
	query.position = target_position
	query.collision_mask = collision_mask # Используем ту же маску, что и у игрока

	var results := space_state.intersect_point(query)

	# Ищем среди результатов BaseCell
	for result in results:
		var collider: Node = result.collider
		if collider is BaseCell:
			return collider

	return null # Клетка не найдена

## Уведомляет игрока о типе поверхности, на которую он приземлился
## Игрок сам решает, как изменить свое физическое состояние
func notify_landed_on_surface(surface: SurfaceType.Type) -> void:
	match surface:
		SurfaceType.Type.LAND:
			_apply_land_physics_state()
		SurfaceType.Type.WATER:
			_apply_water_physics_state()

## Применяет физическое состояние для нахождения на суше
func _apply_land_physics_state() -> void:
	set_collision_layer_value(PLAYER_LAYER_DEFAULT, true)
	set_collision_layer_value(PLAYER_LAYER_SUBMERGED, false)

## Применяет физическое состояние для нахождения в воде
func _apply_water_physics_state() -> void:
	set_collision_layer_value(PLAYER_LAYER_DEFAULT, false)
	set_collision_layer_value(PLAYER_LAYER_SUBMERGED, true)
