class_name SlowEnemiesOnPaintedAbility
extends Node2D

@export var full_boost_multiplier: float = 1.0
@export var tactical_boost_multiplier: float = 0.3 # Множитель для "сдержанного" ускорения

func _ready() -> void:
	# Подписываемся на сообщения о движении и остановке игрока
	Pigeon.subscribe(PlayerMoveMessage.ID, self, _on_player_move)
	Pigeon.subscribe(PlayerStopMessage.ID, self, _on_player_stop)

func _exit_tree() -> void:
	Pigeon.unsubscribe(PlayerMoveMessage.ID, self)
	Pigeon.unsubscribe(PlayerStopMessage.ID, self)

## Обрабатывает сообщение о движении игрока
func _on_player_move(msg: PlayerMoveMessage) -> void:
	# Проверяем, является ли ход "тактическим" (между двумя закрашенными клетками)
	var is_tactical_move: bool = _is_tactical_move(msg.start_cell, msg.destination_cell)

	# Определяем множитель ускорения
	var multiplier: float = tactical_boost_multiplier if is_tactical_move else full_boost_multiplier

	# Получаем всех врагов и ускоряем их
	var enemies: Array[Node] = get_tree().get_nodes_in_group("enemies")
	for enemy in enemies:
		if enemy.has_method("boost_speed"):
			enemy.call("boost_speed", multiplier)

## Обрабатывает сообщение об остановке игрока
func _on_player_stop(_msg: PlayerStopMessage) -> void:
	# Возвращаем всех врагов к медленному состоянию
	var enemies: Array[Node] = get_tree().get_nodes_in_group("enemies")
	for enemy in enemies:
		if enemy.has_method("reset_speed"):
			enemy.call("reset_speed")

## Проверяет, является ли ход "тактическим"
## Возвращает true, если игрок перемещается между двумя закрашенными клетками
func _is_tactical_move(_start_cell: BaseCell, destination_cell: BaseCell) -> bool:
	if not (destination_cell is ColorCell):
		return false

	var destination_color_cell := destination_cell as ColorCell
	return destination_color_cell.colored
