class_name ShieldAbility
extends Node2D

var is_active: bool = true

func _ready() -> void:
	# Добавляем себя в группу обработчиков урона
	add_to_group("damage_handlers")

# Больше не нужны _exit_tree и подписки на Pigeon!

func handle_damage(msg: DamageMessage) -> bool:
	if not is_active:
		return false # Щит неактивен, урон не обработан

	is_active = false
	# Тут можно добавить анимацию/звук щита
	_play_shield_effect()

	# Логика отскока врага
	var player: Node2D = get_owner() as Node2D
	if player and msg.source:
		# Нормаль направлена от центра щита (игрока) к врагу
		var bounce_normal: Vector2 = (msg.source.global_position - player.global_position).normalized()
		# Вызываем метод отскока напрямую, если он есть у врага
		if msg.source.has_method("apply_bounce"):
			msg.source.call("apply_bounce", bounce_normal)

	print("Shield blocked the damage!")
	return true # Урон успешно обработан

func _play_shield_effect() -> void:
	# Заглушка для будущих эффектов
	print("Shield activated!")

func reactivate_shield() -> void:
	"""Публичный метод для переактивации щита (может понадобиться для других механик)"""
	is_active = true
