class_name Enemy
extends CharacterBody2D

@export_range(0.0, 1500.0, 100.0) var _initial_velocity: float
@export_range(0.0, 1.0, 0.1) var _slow_factor: float
@export_range(0.0, 1.0, 0.1) var _angle_deviation: float

@export var _animation_player: AnimationPlayer
@export var _damage_area: Area2D

var _screen_size: Vector2 = Vector2()

func _ready() -> void:
	_animation_player.play("spawn")
	await _animation_player.animation_finished

	_damage_area.area_entered.connect(_on_damage_area_entered)
	add_to_group("enemies")

	randomize()
	var initial_angle: float = randf_range(0, 2 * PI)
	var initial_velocity_vector: Vector2 = Vector2(cos(initial_angle), sin(initial_angle)) * _initial_velocity * _slow_factor
	velocity = initial_velocity_vector
	_screen_size = get_viewport_rect().size

## Устанавливает скорость врага на основе базовой скорости и множителя ускорения.
## boost_multiplier = 1.0 -> полная скорость.
## boost_multiplier = 0.3 -> 30% от полной скорости.
func boost_speed(boost_multiplier: float) -> void:
	var direction: Vector2 = velocity.normalized()
	velocity = direction * _initial_velocity * boost_multiplier

## Возвращает скорость врага к медленному состоянию по умолчанию.
func reset_speed() -> void:
	var direction: Vector2 = velocity.normalized()
	velocity = direction * _initial_velocity * _slow_factor

func _physics_process(delta: float) -> void:
	var motion: Vector2 = velocity * delta
	var collision: KinematicCollision2D = move_and_collide(motion)

	if collision:
		_bounce(collision)

func _bounce(collision: KinematicCollision2D) -> void:
	var normal: Vector2 = collision.get_normal()
	velocity = velocity.bounce(normal)
	_apply_random_deviation()

func _apply_random_deviation() -> void:
	var angle_deviation: float = randf_range(-_angle_deviation, _angle_deviation)
	velocity = velocity.rotated(angle_deviation)

func _on_damage_area_entered(area: Area2D) -> void:
	Pigeon.send_to(area, DamageMessage.new(self))

func apply_bounce(normal: Vector2) -> void:
	"""Публичный метод для отскока от щита"""
	velocity = normal * _initial_velocity
	_apply_random_deviation()
