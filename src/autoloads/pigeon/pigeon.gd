extends Node

@export var logs: bool

# <event_id: String,
# subscription_dictionary: Dictionary<subscriber: Object, subscription: MessageSubscription>>
@onready var subscription_dictionary: Dictionary = Dictionary()


func subscribe(event_id: String, subscriber: Object, method_to_call: Callable) -> void:
	var subscription := MessageSubscription.new(event_id, subscriber, method_to_call)
	_add_subscription(subscription)
	_log("Subscribed: Message '%s', Subscriber '%s'" % [event_id, subscriber])


func unsubscribe(event_id: String, subscriber: Object) -> void:
	if not subscription_dictionary.has(event_id):
		push_error("Unsubscribe failed: Message '%s' not found in subscription dictionary." % event_id)
		return

	var event_subscriptions := subscription_dictionary.get(event_id) as Dictionary
	if not event_subscriptions.has(subscriber):
		push_error("Unsubscribe failed: Subscriber '%s' not found for event '%s'." % [subscriber, event_id])
		return

	event_subscriptions.erase(subscriber)
	_log("Unsubscribed: Message '%s', Subscriber '%s'" % [event_id, subscriber])

	if event_subscriptions.is_empty():
		subscription_dictionary.erase(event_id)
		_log("Removed event '%s' from subscription dictionary due to no subscribers." % event_id)


func send(event: Message) -> void:
	var event_id := event.event_id
	if not subscription_dictionary.has(event_id):
		push_warning("Broadcast failed: No subscribers found for event '%s'" % event_id)
		return

	var event_subscriptions := subscription_dictionary.get(event_id) as Dictionary
	for subscriber: Object in event_subscriptions.keys():
		var existing_sub := event_subscriptions.get(subscriber) as MessageSubscription
		if not existing_sub.method_to_call.is_valid():
			event_subscriptions.erase(subscriber)
			_log("Removed invalid subscription: Message '%s', Subscriber '%s'" % [event_id, subscriber])
			continue

		existing_sub.method_to_call.call(event)
		_log("Sent event '%s' to subscriber '%s'" % [event_id, subscriber])


func send_to(subscriber: Object, event: Message) -> Variant:
	var event_id := event.event_id
	if not subscription_dictionary.has(event_id):
		push_warning("Send failed: No subscriptions found for event '%s'" % event_id)
		return null

	var event_subscriptions := subscription_dictionary.get(event_id) as Dictionary
	if not event_subscriptions.has(subscriber):
		push_warning("Send failed: Subscriber '%s' not found for event '%s'." % [subscriber, event_id])
		return null

	var existing_sub := event_subscriptions.get(subscriber) as MessageSubscription
	if not existing_sub.method_to_call.is_valid():
		event_subscriptions.erase(subscriber)
		push_warning("Send failed: Invalid method_to_call for subscriber '%s' and event '%s'." % [subscriber, event_id])
		return null

	await existing_sub.method_to_call.call(event)
	_log("Sent event '%s' to specific subscriber '%s'" % [event_id, subscriber])
	return event.result


func _add_subscription(subscription: MessageSubscription) -> void:
	var event_id := subscription.event_id
	var subscriber := subscription.subscriber

	if not subscription_dictionary.has(event_id):
		subscription_dictionary[event_id] = Dictionary()
		print("Added new event '%s' to subscription dictionary." % event_id)

	var event_subscriptions := subscription_dictionary.get(event_id) as Dictionary
	event_subscriptions[subscriber] = subscription
	_log("Added subscription: Message '%s', Subscriber '%s'" % [event_id, subscriber])


func _log(message: String) -> void:
	if logs:
		print(message)
